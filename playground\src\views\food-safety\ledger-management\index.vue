<template>
  <div class="h-full">
    <VbenCard class="h-full" title="台账管理">
      <template #extra>
        <div class="flex space-x-2">
          <VbenButton type="primary" @click="handleExportData">
            <template #icon>
              <lucide-icon name="download" />
            </template>
            导出数据
          </VbenButton>
          <VbenButton @click="handleAIReview">
            <template #icon>
              <lucide-icon name="sparkles" />
            </template>
            AI检查
          </VbenButton>
        </div>
      </template>

      <div class="grid h-full grid-cols-1 gap-4 lg:grid-cols-4">
        <!-- 左侧：筛选和统计 -->
        <div class="flex flex-col space-y-4 lg:col-span-1">
          <VbenCard title="筛选条件">
            <VbenForm
              v-model:model="filterForm"
              :schema="filterSchema"
              :show-feedback="false"
              class="w-full"
            />
            <div class="mt-4 flex space-x-2">
              <VbenButton type="primary" @click="handleSearch" size="small">
                搜索
              </VbenButton>
              <VbenButton @click="handleReset" size="small">重置</VbenButton>
            </div>
          </VbenCard>

          <VbenCard title="统计概览">
            <div class="space-y-4">
              <div class="flex items-center justify-between">
                <span class="text-sm text-muted-foreground">总记录数</span>
                <span class="text-2xl font-bold">{{ totalRecords }}</span>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-sm text-muted-foreground">本月新增</span>
                <span class="text-xl font-semibold text-blue-600">{{ monthlyNew }}</span>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-sm text-muted-foreground">待完善</span>
                <span class="text-xl font-semibold text-orange-600">{{ pendingReview }}</span>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-sm text-muted-foreground">已完成</span>
                <span class="text-xl font-semibold text-green-600">{{ completedRecords }}</span>
              </div>
            </div>
          </VbenCard>

          <VbenCard title="AI提醒" v-if="aiReminders.length > 0">
            <div class="space-y-2">
              <div
                v-for="(reminder, index) in aiReminders"
                :key="index"
                class="rounded border-l-4 border-orange-400 bg-orange-50 p-3"
              >
                <div class="text-sm font-medium text-orange-800">{{ reminder.type }}</div>
                <div class="text-xs text-orange-700">{{ reminder.message }}</div>
                <VbenButton
                  size="small"
                  type="link"
                  @click="handleFixReminder(reminder.id)"
                >
                  立即处理
                </VbenButton>
              </div>
            </div>
          </VbenCard>
        </div>

        <!-- 右侧：数据表格 -->
        <div class="flex flex-col space-y-4 lg:col-span-3">
          <VbenCard title="台账记录" class="flex-1">
            <VbenTable
              :columns="ledgerColumns"
              :data="ledgerData"
              :pagination="paginationConfig"
              :loading="loading"
            >
              <template #type="{ row }">
                <VbenTag :color="typeColorMap[row.type]">
                  {{ typeLabelMap[row.type] }}
                </VbenTag>
              </template>

              <template #status="{ row }">
                <VbenTag :color="statusColorMap[row.status]">
                  {{ statusLabelMap[row.status] }}
                </VbenTag>
              </template>

              <template #attachments="{ row }">
                <div class="flex items-center space-x-1">
                  <lucide-icon
                    v-for="attachment in row.attachments"
                    :key="attachment.id"
                    :name="getAttachmentIcon(attachment.type)"
                    class="h-4 w-4 text-muted-foreground"
                  />
                  <span class="text-xs">{{ row.attachments.length }}</span>
                </div>
              </template>

              <template #action="{ row }">
                <div class="flex space-x-1">
                  <VbenButton size="small" @click="handleViewDetail(row)">
                    详情
                  </VbenButton>
                  <VbenButton
                    size="small"
                    type="primary"
                    @click="handleEdit(row)"
                  >
                    编辑
                  </VbenButton>
                  <VbenButton
                    size="small"
                    type="error"
                    @click="handleDelete(row)"
                  >
                    删除
                  </VbenButton>
                </div>
              </template>
            </VbenTable>
          </VbenCard>
        </div>
      </div>

      <!-- 详情对话框 -->
      <VbenModal
        v-model:show="showDetailModal"
        title="台账详情"
        :width="800"
      >
        <div v-if="currentRecord" class="space-y-4">
          <div class="grid grid-cols-2 gap-4">
            <div>
              <h4 class="font-semibold">基本信息</h4>
              <div class="mt-2 space-y-1 text-sm">
                <div><span class="font-medium">编号：</span>{{ currentRecord.id }}</div>
                <div><span class="font-medium">类型：</span>{{ typeLabelMap[currentRecord.type] }}</div>
                <div><span class="font-medium">状态：</span>{{ statusLabelMap[currentRecord.status] }}</div>
                <div><span class="font-medium">创建时间：</span>{{ currentRecord.createdAt }}</div>
              </div>
            </div>
            <div>
              <h4 class="font-semibold">相关人员</h4>
              <div class="mt-2 space-y-1 text-sm">
                <div><span class="font-medium">负责人：</span>{{ currentRecord.assignee }}</div>
                <div><span class="font-medium">创建人：</span>{{ currentRecord.creator }}</div>
              </div>
            </div>
          </div>

          <div>
            <h4 class="font-semibold">内容摘要</h4>
            <div class="mt-2 rounded bg-muted p-3 text-sm">
              {{ currentRecord.summary }}
            </div>
          </div>

          <div>
            <h4 class="font-semibold">附件列表</h4>
            <div class="mt-2 space-y-1">
              <div
                v-for="attachment in currentRecord.attachments"
                :key="attachment.id"
                class="flex items-center justify-between rounded border p-2"
              >
                <div class="flex items-center space-x-2">
                  <lucide-icon
                    :name="getAttachmentIcon(attachment.type)"
                    class="h-4 w-4"
                  />
                  <span class="text-sm">{{ attachment.name }}</span>
                </div>
                <VbenButton size="small" @click="handleDownloadAttachment(attachment)">
                  下载
                </VbenButton>
              </div>
            </div>
          </div>

          <div>
            <h4 class="font-semibold">操作记录</h4>
            <div class="mt-2 space-y-2">
              <div
                v-for="log in currentRecord.operationLogs"
                :key="log.id"
                class="flex items-center space-x-2 text-sm"
              >
                <div class="h-2 w-2 rounded-full bg-blue-500"></div>
                <span>{{ log.time }} - {{ log.action }} - {{ log.operator }}</span>
              </div>
            </div>
          </div>
        </div>
      </VbenModal>
    </VbenCard>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useMessage } from '@vben/hooks';
import { getLedgerData, getAIReview, exportLedgerData } from '#/api/food-safety';

interface Attachment {
  id: string;
  name: string;
  type: string;
  url: string;
}

interface OperationLog {
  id: string;
  time: string;
  action: string;
  operator: string;
}

interface LedgerRecord {
  id: string;
  type: 'inspection' | 'complaint' | 'document' | 'training' | 'other';
  title: string;
  summary: string;
  status: 'pending' | 'in-progress' | 'completed' | 'archived';
  assignee: string;
  creator: string;
  createdAt: string;
  updatedAt: string;
  attachments: Attachment[];
  operationLogs: OperationLog[];
}

const { createMessage } = useMessage();

const filterForm = ref({
  type: '',
  status: '',
  dateRange: [],
  assignee: '',
  keyword: '',
});

const loading = ref(false);
const ledgerData = ref<LedgerRecord[]>([]);
const allData = ref<LedgerRecord[]>([]);
const aiReminders = ref<Array<{ id: string; type: string; message: string }>>([]);
const showDetailModal = ref(false);
const currentRecord = ref<LedgerRecord | null>(null);

const typeColorMap = {
  inspection: 'blue',
  complaint: 'red',
  document: 'green',
  training: 'purple',
  other: 'gray',
};

const typeLabelMap = {
  inspection: '检查记录',
  complaint: '投诉处理',
  document: '文书生成',
  training: '培训记录',
  other: '其他',
};

const statusColorMap = {
  pending: 'orange',
  'in-progress': 'blue',
  completed: 'green',
  archived: 'gray',
};

const statusLabelMap = {
  pending: '待处理',
  'in-progress': '进行中',
  completed: '已完成',
  archived: '已归档',
};

const filterSchema = [
  {
    component: 'VbenSelect',
    componentProps: {
      options: [
        { label: '全部类型', value: '' },
        { label: '检查记录', value: 'inspection' },
        { label: '投诉处理', value: 'complaint' },
        { label: '文书生成', value: 'document' },
        { label: '培训记录', value: 'training' },
        { label: '其他', value: 'other' },
      ],
    },
    fieldName: 'type',
    label: '类型',
  },
  {
    component: 'VbenSelect',
    componentProps: {
      options: [
        { label: '全部状态', value: '' },
        { label: '待处理', value: 'pending' },
        { label: '进行中', value: 'in-progress' },
        { label: '已完成', value: 'completed' },
        { label: '已归档', value: 'archived' },
      ],
    },
    fieldName: 'status',
    label: '状态',
  },
  {
    component: 'VbenDatePicker',
    componentProps: {
      range: true,
      placeholder: ['开始日期', '结束日期'],
    },
    fieldName: 'dateRange',
    label: '日期范围',
  },
  {
    component: 'VbenSelect',
    componentProps: {
      options: [
        { label: '全部人员', value: '' },
        { label: '张三', value: '张三' },
        { label: '李四', value: '李四' },
        { label: '王五', value: '王五' },
        { label: '赵六', value: '赵六' },
      ],
    },
    fieldName: 'assignee',
    label: '负责人',
  },
  {
    component: 'VbenInput',
    componentProps: {
      placeholder: '搜索关键词...',
    },
    fieldName: 'keyword',
    label: '关键词',
  },
];

const ledgerColumns = [
  {
    title: '编号',
    key: 'id',
    width: 100,
  },
  {
    title: '类型',
    key: 'type',
  },
  {
    title: '标题',
    key: 'title',
    width: 200,
  },
  {
    title: '状态',
    key: 'status',
  },
  {
    title: '负责人',
    key: 'assignee',
  },
  {
    title: '创建时间',
    key: 'createdAt',
  },
  {
    title: '附件',
    key: 'attachments',
    width: 80,
  },
  {
    title: '操作',
    key: 'action',
    width: 120,
  },
];

const totalRecords = computed(() => allData.value.length);
const monthlyNew = computed(() =>
  allData.value.filter(
    (record) =>
      new Date(record.createdAt) >=
      new Date(new Date().setMonth(new Date().getMonth() - 1))
  ).length
);
const pendingReview = computed(() =>
  allData.value.filter((record) => record.status === 'pending').length
);
const completedRecords = computed(() =>
  allData.value.filter((record) => record.status === 'completed').length
);

const paginationConfig = {
  pageSize: 10,
  showSizeChanger: true,
  showQuickJumper: true,
};

const handleSearch = () => {
  let filtered = [...allData.value];

  if (filterForm.value.type) {
    filtered = filtered.filter((record) => record.type === filterForm.value.type);
  }

  if (filterForm.value.status) {
    filtered = filtered.filter((record) => record.status === filterForm.value.status);
  }

  if (filterForm.value.assignee) {
    filtered = filtered.filter((record) => record.assignee === filterForm.value.assignee);
  }

  if (filterForm.value.keyword) {
    const keyword = filterForm.value.keyword.toLowerCase();
    filtered = filtered.filter(
      (record) =>
        record.title.toLowerCase().includes(keyword) ||
        record.summary.toLowerCase().includes(keyword)
    );
  }

  if (filterForm.value.dateRange && filterForm.value.dateRange.length === 2) {
    const [start, end] = filterForm.value.dateRange;
    filtered = filtered.filter(
      (record) =>
        new Date(record.createdAt) >= new Date(start) &&
        new Date(record.createdAt) <= new Date(end)
    );
  }

  ledgerData.value = filtered;
};

const handleReset = () => {
  filterForm.value = {
    type: '',
    status: '',
    dateRange: [],
    assignee: '',
    keyword: '',
  };
  ledgerData.value = [...allData.value];
};

const handleAIReview = async () => {
  try {
    const result = await getAIReview(allData.value);
    aiReminders.value = result.reminders;
    createMessage.success('AI检查完成');
  } catch (error) {
    createMessage.error('检查失败，请重试');
  }
};

const handleExportData = async () => {
  try {
    const result = await exportLedgerData({
      data: ledgerData.value,
      format: 'excel',
    });

    const blob = new Blob([result.data], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `食品安全台账_${new Date().toISOString().split('T')[0]}.xlsx`;
    a.click();
    URL.revokeObjectURL(url);

    createMessage.success('导出成功');
  } catch (error) {
    createMessage.error('导出失败，请重试');
  }
};

const handleViewDetail = (record: LedgerRecord) => {
  currentRecord.value = record;
  showDetailModal.value = true;
};

const handleEdit = (record: LedgerRecord) => {
  // 编辑逻辑
  createMessage.info('编辑功能开发中');
};

const handleDelete = (record: LedgerRecord) => {
  // 删除逻辑
  const index = ledgerData.value.findIndex((r) => r.id === record.id);
  if (index !== -1) {
    ledgerData.value.splice(index, 1);
    createMessage.success('记录已删除');
  }
};

const handleFixReminder = (reminderId: string) => {
  // 处理AI提醒的逻辑
  const reminder = aiReminders.value.find((r) => r.id === reminderId);
  if (reminder) {
    createMessage.info(`正在处理：${reminder.type}`);
  }
};

const getAttachmentIcon = (type: string) => {
  const iconMap: Record<string, string> = {
    pdf: 'file-text',
    doc: 'file-word',
    docx: 'file-word',
    jpg: 'file-image',
    jpeg: 'file-image',
    png: 'file-image',
  };
  return iconMap[type] || 'file';
};

const handleDownloadAttachment = (attachment: Attachment) => {
  // 下载附件逻辑
  window.open(attachment.url, '_blank');
};

const loadData = async () => {
  loading.value = true;
  try {
    const result = await getLedgerData();
    allData.value = result.data;
    ledgerData.value = result.data;
  } catch (error) {
    createMessage.error('加载数据失败');
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  loadData();
});
</script>