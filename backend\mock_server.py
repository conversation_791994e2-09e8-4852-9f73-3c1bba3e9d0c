from fastapi import FastAPI, File, UploadFile
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Optional
import json
import uuid
from datetime import datetime, timedelta
import os

app = FastAPI(title="食品安全平台Mock API")

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class PPTGenerationRequest(BaseModel):
    requirements: str
    templateStyle: str = "formal"
    theme: str = "food-safety"
    attachments: Optional[List[dict]] = []

class DocumentGenerationRequest(BaseModel):
    templateType: str
    caseType: str
    companyName: str
    companyAddress: str
    legalPerson: str
    inspectionDate: str
    inspectors: str
    violationType: str
    violationDescription: str
    penaltyMeasures: str
    deadline: str
    additionalInfo: str

class ComplaintAnalysisRequest(BaseModel):
    complaint: dict
    evidenceFiles: List[dict]

class AIAdviceRequest(BaseModel):
    task: dict
    availableMembers: List[dict]
    currentWorkload: List[dict]

# Mock数据
MOCK_PPT_BLUEPRINT = {
    "structure": "食品安全培训PPT，包含背景介绍、法规要求、案例分析、总结建议四个部分",
    "slides": [
        {"title": "食品安全培训", "content": "面向餐饮企业员工的食品安全知识普及"},
        {"title": "法规要求", "content": "《食品安全法》相关条款解读"},
        {"title": "案例分析", "content": "近期食品安全事件案例分析"},
        {"title": "总结建议", "content": "企业食品安全管理建议"}
    ]
}

MOCK_DOCUMENT = {
    "title": "现场检查笔录",
    "content": """现场检查笔录

检查时间：2024年1月15日
检查地点：XX餐饮有限公司
检查人员：张三、李四
被检查单位：XX餐饮有限公司
法定代表人：王五

检查情况：
1. 厨房环境卫生状况良好
2. 食品储存符合规范要求
3. 从业人员健康证齐全有效
4. 未发现过期变质食品

处理意见：
本次检查未发现违法违规行为，予以通过。

检查人员签名：张三、李四
被检查单位签名：王五
日期：2024年1月15日"""
}

@app.post("/api/food-safety/ppt/generate")
async def generate_ppt(request: PPTGenerationRequest):
    """模拟PPT生成"""
    return {
        "blueprint": MOCK_PPT_BLUEPRINT,
        "url": "https://example.com/ppt/test.pptx"
    }

@app.post("/api/food-safety/document/generate")
async def generate_document(request: DocumentGenerationRequest):
    """模拟文书生成"""
    return MOCK_DOCUMENT

@app.post("/api/food-safety/complaint/analyze")
async def analyze_complaint(request: ComplaintAnalysisRequest):
    """模拟投诉分析"""
    return {
        "summary": "消费者投诉某餐厅食品安全问题，涉及食品变质和服务态度",
        "suggestedTasks": [
            {
                "description": "现场检查餐厅厨房卫生状况",
                "priority": "high",
                "deadline": (datetime.now() + timedelta(days=1)).strftime("%Y-%m-%d"),
                "assignee": "张三"
            },
            {
                "description": "调取餐厅进货台账和检验报告",
                "priority": "medium",
                "deadline": (datetime.now() + timedelta(days=2)).strftime("%Y-%m-%d"),
                "assignee": "李四"
            }
        ]
    }

@app.post("/api/food-safety/complaint/generate-reply")
async def generate_reply():
    """模拟回复生成"""
    return {
        "reply": """关于您反映的食品安全问题，我局已调查处理完毕，现将结果回复如下：

经查，您反映的XX餐厅确实存在部分食品安全管理不规范的问题。我局已责令该餐厅立即整改，具体措施包括：
1. 加强食品储存管理，确保食品新鲜
2. 规范食品加工操作流程
3. 加强从业人员培训

该餐厅已按要求完成整改，我局将继续加强监管。感谢您对食品安全工作的关注和支持。"""
    }

@app.post("/api/food-safety/task/ai-advice")
async def get_ai_advice(request: AIAdviceRequest):
    """模拟AI建议"""
    return {
        "suggestions": [
            {"text": "建议分配给张三，他近期工作负载较轻"},
            {"text": "李四在食品安全检查方面有丰富经验"}
        ],
        "recommendedMembers": ["zhangsan", "lisi"]
    }

@app.post("/api/food-safety/task/assign")
async def assign_task():
    """模拟任务分配"""
    return {
        "taskId": str(uuid.uuid4()),
        "success": True
    }

@app.get("/api/food-safety/ledger/data")
async def get_ledger_data():
    """模拟台账数据"""
    return {
        "data": [
            {
                "id": "REC001",
                "type": "inspection",
                "title": "XX餐厅日常检查",
                "summary": "对XX餐厅进行日常食品安全检查",
                "status": "completed",
                "assignee": "张三",
                "creator": "李四",
                "createdAt": "2024-01-15",
                "updatedAt": "2024-01-15",
                "attachments": [
                    {"id": "ATT001", "name": "检查照片.jpg", "type": "jpg", "url": "/uploads/photo1.jpg"}
                ],
                "operationLogs": [
                    {"id": "LOG001", "time": "2024-01-15 09:00", "action": "创建记录", "operator": "李四"}
                ]
            },
            {
                "id": "REC002",
                "type": "complaint",
                "title": "消费者投诉处理",
                "summary": "处理消费者关于食品安全的投诉",
                "status": "in-progress",
                "assignee": "王五",
                "creator": "赵六",
                "createdAt": "2024-01-14",
                "updatedAt": "2024-01-15",
                "attachments": [
                    {"id": "ATT002", "name": "投诉材料.pdf", "type": "pdf", "url": "/uploads/complaint.pdf"}
                ],
                "operationLogs": [
                    {"id": "LOG002", "time": "2024-01-14 10:00", "action": "创建记录", "operator": "赵六"},
                    {"id": "LOG003", "time": "2024-01-15 14:00", "action": "开始处理", "operator": "王五"}
                ]
            }
        ]
    }

@app.post("/api/food-safety/upload")
async def upload_file(file: UploadFile = File(...)):
    """模拟文件上传"""
    return {
        "success": True,
        "file": {
            "id": str(uuid.uuid4()),
            "filename": file.filename,
            "content_type": file.content_type,
            "size": 1024
        }
    }

@app.get("/")
async def root():
    return {"message": "食品安全平台Mock API服务运行正常"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)