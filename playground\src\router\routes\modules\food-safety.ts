import type { RouteRecordRaw } from 'vue-router';

import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'lucide:shield-check',
      order: 1,
      title: '食品安全管理',
    },
    name: 'FoodSafety',
    path: '/food-safety',
    children: [
      {
        name: 'PPTGeneration',
        path: '/ppt-generation',
        component: () => import('#/views/food-safety/ppt-generation/index.vue'),
        meta: {
          icon: 'lucide:presentation',
          title: 'PPT智能生成',
        },
      },
      {
        name: 'DocumentGeneration',
        path: '/document-generation',
        component: () => import('#/views/food-safety/document-generation/index.vue'),
        meta: {
          icon: 'lucide:file-text',
          title: '文书智能生成',
        },
      },
      {
        name: 'ComplaintHandling',
        path: '/complaint-handling',
        component: () => import('#/views/food-safety/complaint-handling/index.vue'),
        meta: {
          icon: 'lucide:message-square-warning',
          title: '投诉举报处理',
        },
      },
      {
        name: 'TaskAssignment',
        path: '/task-assignment',
        component: () => import('#/views/food-safety/task-assignment/index.vue'),
        meta: {
          icon: 'lucide:calendar-clock',
          title: '任务分配监控',
        },
      },
      {
        name: 'LedgerManagement',
        path: '/ledger-management',
        component: () => import('#/views/food-safety/ledger-management/index.vue'),
        meta: {
          icon: 'lucide:book-open',
          title: '台账管理',
        },
      },
    ],
  },
];

export default routes;