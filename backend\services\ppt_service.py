import json
from typing import Dict, List, Any, Optional
from langchain.chat_models import ChatOpenAI
from langchain.schema import SystemMessage, HumanMessage
from langchain.chains import LL<PERSON>hain
from langchain.prompts import ChatPromptTemplate
import asyncio

class PPTService:
    def __init__(self):
        # 初始化语言模型
        self.llm = ChatOpenAI(
            model="gpt-3.5-turbo",
            temperature=0.7,
            openai_api_key="your-api-key-here"  # 替换为实际API密钥或使用环境变量
        )
        
    async def generate_blueprint(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """生成PPT蓝图"""
        requirements = request.get('requirements', '')
        template_style = request.get('templateStyle', 'formal')
        theme = request.get('theme', 'food-safety')
        
        # 创建提示模板
        prompt = ChatPromptTemplate.from_messages([
            SystemMessage(content="""你是一个专业的食品安全培训内容策划师。
            请根据用户需求生成PPT的结构化蓝图。
            蓝图应包括：
            1. 整体结构描述
            2. 每页幻灯片的标题和内容要点
            3. 适合的视觉元素建议
            请确保内容专业、准确、易于理解。"""),
            HumanMessage(content=f"""
            需求：{requirements}
            模板风格：{template_style}
            主题类型：{theme}
            
            请生成详细的PPT蓝图。
            """)
        ])
        
        # 生成响应
        chain = LLMChain(llm=self.llm, prompt=prompt)
        response = await chain.arun({})
        
        # 解析响应为结构化数据
        try:
            # 尝试从响应中提取JSON
            import re
            json_pattern = r'\{[^}]*\}'
            matches = re.findall(json_pattern, response, re.DOTALL)
            
            if matches:
                blueprint = json.loads(matches[0])
            else:
                # 如果没有JSON，创建默认结构
                blueprint = {
                    "structure": f"基于{theme}主题的{template_style}风格PPT",
                    "slides": [
                        {
                            "title": "封面",
                            "content": requirements[:50] + "..." if len(requirements) > 50 else requirements
                        },
                        {
                            "title": "目录",
                            "content": "1. 背景介绍\n2. 主要内容\n3. 总结建议"
                        }
                    ]
                }
        except:
            # 如果解析失败，创建基础结构
            blueprint = {
                "structure": f"食品安全{theme}主题PPT",
                "slides": [
                    {"title": "封面", "content": requirements},
                    {"title": "背景", "content": "食品安全重要性"},
                    {"title": "主要内容", "content": "详细内容待补充"},
                    {"title": "总结", "content": "行动建议"}
                ]
            }
        
        return blueprint
    
    async def generate_final_ppt(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """生成最终PPT"""
        blueprint = request.get('blueprint')
        
        # 这里集成实际的PPT生成服务
        # 例如：使用python-pptx库或调用外部API
        
        # 模拟生成过程
        ppt_url = f"https://example.com/ppt/{uuid.uuid4()}.pptx"
        
        return {
            "url": ppt_url,
            "message": "PPT生成成功"
        }
    
    async def handle_chat_interaction(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """处理用户对话交互"""
        chat = request.get('chat', '')
        context = request.get('context', [])
        
        # 构建对话上下文
        messages = [
            SystemMessage(content="""你是一个PPT编辑助手。
            请根据用户的修改要求调整PPT内容。
            回复应包含具体的修改建议。""")
        ]
        
        # 添加上下文
        for msg in context[-5:]:  # 只保留最近5条消息
            if msg.get('role') == 'user':
                messages.append(HumanMessage(content=msg.get('content', '')))
            else:
                messages.append(SystemMessage(content=msg.get('content', '')))
        
        messages.append(HumanMessage(content=chat))
        
        # 生成回复
        response = await self.llm.agenerate([messages])
        reply = response.generations[0][0].text
        
        return {
            "reply": reply,
            "updatedPptUrl": None  # 实际应用中会更新PPT并返回新URL
        }

# 示例使用
if __name__ == "__main__":
    service = PPTService()
    
    async def test():
        result = await service.generate_blueprint({
            "requirements": "食品安全培训材料，面向餐饮企业员工",
            "templateStyle": "formal",
            "theme": "food-safety"
        })
        print(json.dumps(result, indent=2, ensure_ascii=False))
    
    asyncio.run(test())