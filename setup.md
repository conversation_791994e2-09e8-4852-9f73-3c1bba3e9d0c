# 食品安全管理平台 - 设置指南

## 项目概述

这是一个基于Vue Vben Admin开发的AI赋能食品安全管理平台，专为县级市场监督管理局食品科设计，提供PPT智能生成、文书自动生成、投诉举报处理、任务分配监控和台账管理等功能。

## 环境要求

### 前端环境
- Node.js ≥ 20.10.0
- pnpm ≥ 9.12.0
- Git

### 后端环境
- Python ≥ 3.8
- PostgreSQL ≥ 12
- Redis ≥ 6.0

## 快速开始

### 1. 前端环境配置

```bash
# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev:play

# 访问 http://localhost:5173
```

### 2. 后端环境配置

#### 2.1 创建Python虚拟环境
```bash
cd backend
python -m venv venv

# Windows
venv\Scripts\activate

# Linux/Mac
source venv/bin/activate
```

#### 2.2 安装依赖
```bash
pip install -r requirements.txt
```

#### 2.3 配置环境变量
创建 `.env` 文件：
```bash
# 数据库配置
DATABASE_URL=postgresql://username:password@localhost:5432/food_safety_db
REDIS_URL=redis://localhost:6379/0

# OpenAI API密钥
OPENAI_API_KEY=your-openai-api-key

# 文件存储配置
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=10485760  # 10MB
```

#### 2.4 初始化数据库
```bash
# 创建数据库表
alembic upgrade head

# 导入初始数据（可选）
python scripts/init_data.py
```

### 3. AI模型配置

#### 3.1 OpenAI配置
确保已获取OpenAI API密钥，并配置在环境变量中。

#### 3.2 本地AI模型（可选）
如需使用本地Ollama模型：
```bash
# 安装Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# 下载模型
ollama pull llama2:7b
```

### 4. 启动服务

#### 4.1 启动后端服务
```bash
# 开发模式
python main.py

# 或使用uvicorn
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

#### 4.2 启动前端服务
```bash
# 在另一个终端
pnpm dev:play
```

### 5. 访问系统

- 前端地址：http://localhost:5173
- 后端API：http://localhost:8000
- API文档：http://localhost:8000/docs

## 功能模块说明

### 1. PPT智能生成
- **功能**：通过自然语言描述生成食品安全培训PPT
- **使用**：输入需求描述 → AI生成蓝图 → 确认修改 → 生成PPT

### 2. 文书智能生成
- **功能**：自动生成各类食品安全执法文书
- **模板**：现场检查笔录、责令改正通知书、行政处罚决定书等

### 3. 投诉举报处理
- **流程**：上传投诉材料 → AI分析生成任务 → 执行任务 → 生成回复

### 4. 任务分配监控
- **功能**：智能任务分配、甘特图监控、进度跟踪
- **AI特性**：基于工作负载智能推荐任务分配

### 5. 台账管理
- **功能**：自动汇总所有操作记录、AI完整性检查、数据导出

## 开发说明

### 前端开发
```bash
# 代码检查
pnpm lint

# 类型检查
pnpm check:type

# 构建生产版本
pnpm build:play
```

### 后端开发
```bash
# 运行测试
pytest tests/

# 代码格式化
black .
isort .

# 类型检查
mypy .
```

## 部署说明

### 生产环境部署

#### Docker部署
```bash
# 构建镜像
docker build -t food-safety-platform .

# 运行容器
docker run -d \
  --name food-safety-app \
  -p 8000:8000 \
  -v $(pwd)/uploads:/app/uploads \
  --env-file .env \
  food-safety-platform
```

#### 传统部署
1. 配置生产环境变量
2. 使用gunicorn或uvicorn运行
3. 配置Nginx反向代理
4. 设置SSL证书

### 数据库备份
```bash
# 备份数据库
pg_dump food_safety_db > backup_$(date +%Y%m%d_%H%M%S).sql

# 恢复数据库
psql food_safety_db < backup_file.sql
```

## 故障排除

### 常见问题

1. **依赖安装失败**
   - 确保Python版本符合要求
   - 使用国内镜像源：`pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple`

2. **数据库连接失败**
   - 检查PostgreSQL服务是否启动
   - 验证连接字符串配置

3. **AI服务不可用**
   - 检查API密钥配置
   - 确认网络连接正常

4. **文件上传失败**
   - 检查上传目录权限
   - 验证文件大小限制

### 日志查看

#### 后端日志
```bash
# 查看应用日志
tail -f logs/app.log

# 查看错误日志
tail -f logs/error.log
```

#### 前端日志
```bash
# 查看开发服务器日志
pnpm dev:play --verbose
```

## 技术支持

如有问题，请联系开发团队或提交GitHub issue。

## 许可证

本项目基于MIT许可证开源，详见LICENSE文件。