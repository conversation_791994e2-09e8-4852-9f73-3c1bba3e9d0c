<template>
  <div class="h-full">
    <VbenCard class="h-full" title="任务分配监控">
      <template #extra>
        <VbenButton type="primary" @click="handleCreateTask">
          <template #icon>
            <lucide-icon name="plus" />
          </template>
          新建任务
        </VbenButton>
      </template>

      <div class="grid h-full grid-cols-1 gap-4 lg:grid-cols-3">
        <!-- 左侧：任务表单 -->
        <div class="flex flex-col space-y-4 lg:col-span-1">
          <VbenCard title="任务信息" class="flex-1">
            <VbenForm
              v-model:model="taskForm"
              :schema="taskSchema"
              :show-feedback="false"
              class="w-full"
            />
          </VbenCard>

          <VbenCard title="附件上传">
            <VbenUpload
              v-model:file-list="taskFiles"
              accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
              :multiple="true"
              :max-count="5"
              class="w-full"
            >
              <VbenButton type="default">
                <template #icon>
                  <lucide-icon name="upload" />
                </template>
                选择文件
              </VbenButton>
            </VbenUpload>
          </VbenCard>

          <VbenCard title="人员分配">
            <div class="space-y-2">
              <VbenSelect
                v-model="selectedMembers"
                :options="teamMembers"
                multiple
                placeholder="选择任务执行人员"
                class="w-full"
              />
              
              <div v-if="aiSuggestions.length > 0" class="rounded bg-blue-50 p-3">
                <div class="text-sm font-medium text-blue-900 mb-2">AI建议：</div>
                <div class="space-y-1">
                  <div
                    v-for="(suggestion, index) in aiSuggestions"
                    :key="index"
                    class="text-sm text-blue-800"
                  >
                    {{ suggestion.text }}
                  </div>
                </div>
              </div>
            </div>
          </VbenCard>

          <div class="flex space-x-2">
            <VbenButton
              type="primary"
              @click="handleGetAIAdvice"
              :loading="gettingAIAdvice"
            >
              AI建议
            </VbenButton>
            <VbenButton
              type="success"
              @click="handleAssignTask"
              :loading="assigning"
            >
              分配任务
            </VbenButton>
          </div>
        </div>

        <!-- 右侧：甘特图和任务列表 -->
        <div class="flex flex-col space-y-4 lg:col-span-2">
          <VbenCard title="任务甘特图" class="flex-1">
            <div class="h-96">
              <div v-if="!tasks.length" class="flex h-full items-center justify-center">
                <div class="text-center">
                  <lucide-icon name="calendar-clock" class="mx-auto h-12 w-12 text-muted-foreground" />
                  <p class="mt-2 text-sm text-muted-foreground">暂无任务，请从左侧创建</p>
                </div>
              </div>
              
              <div v-else class="h-full">
                <!-- 简化的甘特图实现 -->
                <div class="space-y-2">
                  <div class="flex items-center justify-between text-sm font-medium">
                    <span>任务进度总览</span>
                    <span>{{ completedTasks }}/{{ tasks.length }} 已完成</span>
                  </div>
                  
                  <div class="w-full rounded-full bg-gray-200">
                    <div
                      class="rounded-full bg-blue-600 py-1 text-center text-xs font-medium text-white"
                      :style="{ width: `${(completedTasks / tasks.length) * 100}%` }"
                    >
                      {{ Math.round((completedTasks / tasks.length) * 100) }}%
                    </div>
                  </div>
                </div>

                <!-- 时间轴视图 -->
                <div class="mt-4 space-y-2 overflow-auto">
                  <div
                    v-for="task in tasks"
                    :key="task.id"
                    class="rounded border p-3"
                  >
                    <div class="flex items-center justify-between">
                      <div>
                        <h4 class="font-medium">{{ task.title }}</h4>
                        <p class="text-sm text-muted-foreground">{{ task.description }}</p>
                      </div>
                      <div class="flex items-center space-x-2">
                        <VbenTag
                          :color="task.status === 'completed' ? 'success' : 'warning'"
                        >
                          {{ task.status === 'completed' ? '已完成' : '进行中' }}
                        </VbenTag>
                        <VbenButton size="small" @click="handleViewTask(task)">
                          查看
                        </VbenButton>
                      </div>
                    </div>
                    
                    <div class="mt-2 flex items-center justify-between text-xs text-muted-foreground">
                      <span>负责人：{{ task.assignee }}</span>
                      <span>截止日期：{{ task.deadline }}</span>
                    </div>
                    
                    <div class="mt-1">
                      <div class="w-full rounded-full bg-gray-200">
                        <div
                          class="rounded-full bg-green-600 py-0.5"
                          :style="{ width: `${task.progress}%` }"
                        ></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </VbenCard>

          <VbenCard title="任务列表">
            <VbenTable
              :columns="taskColumns"
              :data="tasks"
              :pagination="paginationConfig"
            >
              <template #action="{ row }">
                <div class="flex space-x-2">
                  <VbenButton
                    size="small"
                    type="primary"
                    @click="handleUpdateProgress(row)"
                  >
                    更新进度
                  </VbenButton>
                  <VbenButton
                    size="small"
                    type="success"
                    @click="handleCompleteTask(row)"
                    :disabled="row.status === 'completed'"
                  >
                    完成
                  </VbenButton>
                  <VbenButton
                    size="small"
                    type="error"
                    @click="handleDeleteTask(row)"
                  >
                    删除
                  </VbenButton>
                </div>
              </template>
            </VbenTable>
          </VbenCard>
        </div>
      </div>

      <!-- 任务详情对话框 -->
      <VbenModal
        v-model:show="showTaskModal"
        title="任务详情"
        :width="600"
      >
        <div class="space-y-4">
          <VbenForm
            v-model:model="currentTask"
            :schema="taskDetailSchema"
            :show-feedback="false"
            class="w-full"
          />
          
          <div>
            <h4 class="mb-2 font-semibold">任务进度</h4>
            <VbenSlider v-model="currentTask.progress" :min="0" :max="100" />
            <div class="text-sm text-muted-foreground">
              当前进度：{{ currentTask.progress }}%
            </div>
          </div>
          
          <div class="flex justify-end space-x-2">
            <VbenButton @click="showTaskModal = false">取消</VbenButton>
            <VbenButton type="primary" @click="handleSaveTask">保存</VbenButton>
          </div>
        </div>
      </VbenModal>
    </VbenCard>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useMessage } from '@vben/hooks';
import { getAIAdvice, assignTask, updateTaskProgress } from '#/api/food-safety';

interface Task {
  id: string;
  title: string;
  description: string;
  assignee: string;
  deadline: string;
  status: 'pending' | 'in-progress' | 'completed';
  progress: number;
  priority: 'high' | 'medium' | 'low';
}

const { createMessage } = useMessage();

const taskForm = ref({
  title: '',
  description: '',
  deadline: '',
  priority: 'medium' as 'high' | 'medium' | 'low',
  notes: '',
});

const taskFiles = ref([]);
const selectedMembers = ref<string[]>([]);
const aiSuggestions = ref<Array<{ text: string }>>([]);
const gettingAIAdvice = ref(false);
const assigning = ref(false);
const tasks = ref<Task[]>([]);
const showTaskModal = ref(false);
const currentTask = ref<Task>({} as Task);

const teamMembers = [
  { label: '张三', value: 'zhangsan' },
  { label: '李四', value: 'lisi' },
  { label: '王五', value: 'wangwu' },
  { label: '赵六', value: 'zhaoliu' },
];

const taskSchema = [
  {
    component: 'VbenInput',
    componentProps: {
      placeholder: '请输入任务标题',
    },
    fieldName: 'title',
    label: '任务标题',
    rules: 'required',
  },
  {
    component: 'VbenTextarea',
    componentProps: {
      rows: 3,
      placeholder: '请输入任务描述',
    },
    fieldName: 'description',
    label: '任务描述',
    rules: 'required',
  },
  {
    component: 'VbenDatePicker',
    componentProps: {
      placeholder: '选择截止日期',
    },
    fieldName: 'deadline',
    label: '截止日期',
    rules: 'required',
  },
  {
    component: 'VbenSelect',
    componentProps: {
      options: [
        { label: '高', value: 'high' },
        { label: '中', value: 'medium' },
        { label: '低', value: 'low' },
      ],
    },
    fieldName: 'priority',
    label: '优先级',
  },
  {
    component: 'VbenTextarea',
    componentProps: {
      rows: 2,
      placeholder: '请输入备注信息',
    },
    fieldName: 'notes',
    label: '备注',
  },
];

const taskDetailSchema = [
  {
    component: 'VbenInput',
    componentProps: {
      placeholder: '任务标题',
    },
    fieldName: 'title',
    label: '任务标题',
  },
  {
    component: 'VbenTextarea',
    componentProps: {
      rows: 3,
      placeholder: '任务描述',
    },
    fieldName: 'description',
    label: '任务描述',
  },
  {
    component: 'VbenSelect',
    componentProps: {
      options: teamMembers,
    },
    fieldName: 'assignee',
    label: '负责人',
  },
  {
    component: 'VbenDatePicker',
    componentProps: {
      placeholder: '截止日期',
    },
    fieldName: 'deadline',
    label: '截止日期',
  },
];

const taskColumns = [
  {
    title: '任务标题',
    key: 'title',
  },
  {
    title: '负责人',
    key: 'assignee',
  },
  {
    title: '截止日期',
    key: 'deadline',
  },
  {
    title: '状态',
    key: 'status',
    render: ({ row }) => (
      <VbenTag color={row.status === 'completed' ? 'success' : 'warning'}>
        {row.status === 'completed' ? '已完成' : '进行中'}
      </VbenTag>
    ),
  },
  {
    title: '优先级',
    key: 'priority',
    render: ({ row }) => (
      <VbenTag color={row.priority === 'high' ? 'error' : row.priority === 'medium' ? 'warning' : 'success'}>
        {row.priority === 'high' ? '高' : row.priority === 'medium' ? '中' : '低'}
      </VbenTag>
    ),
  },
  {
    title: '进度',
    key: 'progress',
    render: ({ row }) => `${row.progress}%`,
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
  },
];

const paginationConfig = {
  pageSize: 10,
  showSizeChanger: true,
  showQuickJumper: true,
};

const completedTasks = computed(() =>
  tasks.value.filter(task => task.status === 'completed').length
);

const handleCreateTask = () => {
  taskForm.value = {
    title: '',
    description: '',
    deadline: '',
    priority: 'medium',
    notes: '',
  };
  selectedMembers.value = [];
  taskFiles.value = [];
};

const handleGetAIAdvice = async () => {
  if (!taskForm.value.description.trim()) {
    createMessage.error('请输入任务描述');
    return;
  }

  gettingAIAdvice.value = true;
  try {
    const result = await getAIAdvice({
      task: taskForm.value,
      availableMembers: teamMembers,
      currentWorkload: tasks.value,
    });

    aiSuggestions.value = result.suggestions;
    selectedMembers.value = result.recommendedMembers;
  } catch (error) {
    createMessage.error('获取建议失败，请重试');
  } finally {
    gettingAIAdvice.value = false;
  }
};

const handleAssignTask = async () => {
  if (!taskForm.value.title.trim()) {
    createMessage.error('请输入任务标题');
    return;
  }

  if (!selectedMembers.value.length) {
    createMessage.error('请选择任务执行人员');
    return;
  }

  assigning.value = true;
  try {
    const result = await assignTask({
      ...taskForm.value,
      assignees: selectedMembers.value,
      files: taskFiles.value,
    });

    tasks.value.push({
      id: result.taskId,
      title: taskForm.value.title,
      description: taskForm.value.description,
      assignee: selectedMembers.value.join(', '),
      deadline: taskForm.value.deadline,
      status: 'pending',
      progress: 0,
      priority: taskForm.value.priority,
    });

    createMessage.success('任务分配成功');
    handleCreateTask();
  } catch (error) {
    createMessage.error('分配失败，请重试');
  } finally {
    assigning.value = false;
  }
};

const handleViewTask = (task: Task) => {
  currentTask.value = { ...task };
  showTaskModal.value = true;
};

const handleSaveTask = async () => {
  try {
    await updateTaskProgress({
      taskId: currentTask.value.id,
      progress: currentTask.value.progress,
      status: currentTask.value.progress === 100 ? 'completed' : 'in-progress',
    });

    const index = tasks.value.findIndex(task => task.id === currentTask.value.id);
    if (index !== -1) {
      tasks.value[index] = { ...currentTask.value };
    }

    showTaskModal.value = false;
    createMessage.success('保存成功');
  } catch (error) {
    createMessage.error('保存失败，请重试');
  }
};

const handleUpdateProgress = (task: Task) => {
  handleViewTask(task);
};

const handleCompleteTask = (task: Task) => {
  task.status = 'completed';
  task.progress = 100;
  createMessage.success('任务已完成');
};

const handleDeleteTask = (task: Task) => {
  const index = tasks.value.findIndex(t => t.id === task.id);
  if (index !== -1) {
    tasks.value.splice(index, 1);
    createMessage.success('任务已删除');
  }
};
</script>