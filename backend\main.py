from fastapi import FastAPI, File, UploadFile, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from typing import List, Optional
import uvicorn
import json
import io
from datetime import datetime, timedelta
import uuid

# Import our AI modules
from services.ppt_service import PPTService
from services.document_service import DocumentService
from services.complaint_service import ComplaintService
from services.task_service import TaskService
from services.ledger_service import LedgerService

app = FastAPI(
    title="食品安全管理平台",
    description="AI赋能的县级市场监督管理局食品科管理平台",
    version="1.0.0"
)

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173", "http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 初始化服务
ppt_service = PPTService()
document_service = DocumentService()
complaint_service = ComplaintService()
task_service = TaskService()
ledger_service = LedgerService()

# PPT生成相关模型
class PPTGenerationRequest(BaseModel):
    requirements: str
    templateStyle: str = "formal"
    theme: str = "food-safety"
    attachments: Optional[List[dict]] = []
    confirm: Optional[bool] = False
    chat: Optional[str] = None
    context: Optional[List[dict]] = []

class PPTBlueprintResponse(BaseModel):
    structure: str
    slides: List[dict]
    url: Optional[str] = None

# 文书生成相关模型
class DocumentGenerationRequest(BaseModel):
    templateType: str
    caseType: str
    companyName: str
    companyAddress: str
    legalPerson: str
    inspectionDate: str
    inspectors: str
    violationType: str
    violationDescription: str
    penaltyMeasures: str
    deadline: str
    additionalInfo: str

class DocumentResponse(BaseModel):
    title: str
    content: str
    downloadUrl: Optional[str] = None

# 投诉处理相关模型
class ComplaintAnalysisRequest(BaseModel):
    complaint: dict
    evidenceFiles: List[dict]

class ComplaintAnalysisResponse(BaseModel):
    summary: str
    suggestedTasks: List[dict]

class ReplyGenerationRequest(BaseModel):
    complaint: dict
    investigationResult: str
    completedTasks: List[dict]

class ReplyResponse(BaseModel):
    reply: str

# 任务分配相关模型
class AIAdviceRequest(BaseModel):
    task: dict
    availableMembers: List[dict]
    currentWorkload: List[dict]

class AIAdviceResponse(BaseModel):
    suggestions: List[dict]
    recommendedMembers: List[str]

class TaskAssignmentRequest(BaseModel):
    title: str
    description: str
    deadline: str
    priority: str
    notes: str
    assignees: List[str]
    files: List[dict]

class TaskResponse(BaseModel):
    taskId: str
    success: bool

class TaskProgressRequest(BaseModel):
    taskId: str
    progress: int
    status: str

# 台账管理相关模型
class LedgerExportRequest(BaseModel):
    data: List[dict]
    format: str = "excel"

class AIReviewResponse(BaseModel):
    reminders: List[dict]

# PPT生成API
@app.post("/api/food-safety/ppt/generate", response_model=PPTBlueprintResponse)
async def generate_ppt(request: PPTGenerationRequest):
    """生成PPT蓝图或最终PPT"""
    try:
        if request.chat:
            # 处理对话修改
            result = await ppt_service.handle_chat_interaction(request)
            return result
        elif request.confirm:
            # 生成最终PPT
            result = await ppt_service.generate_final_ppt(request)
            return result
        else:
            # 生成PPT蓝图
            result = await ppt_service.generate_blueprint(request)
            return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# 文书生成API
@app.post("/api/food-safety/document/generate", response_model=DocumentResponse)
async def generate_document(request: DocumentGenerationRequest):
    """生成食品安全相关文书"""
    try:
        result = await document_service.generate_document(request)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# 投诉处理API
@app.post("/api/food-safety/complaint/analyze", response_model=ComplaintAnalysisResponse)
async def analyze_complaint(request: ComplaintAnalysisRequest):
    """分析投诉内容并生成建议任务"""
    try:
        result = await complaint_service.analyze_complaint(request)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/food-safety/complaint/generate-reply", response_model=ReplyResponse)
async def generate_complaint_reply(request: ReplyGenerationRequest):
    """生成投诉回复"""
    try:
        result = await complaint_service.generate_reply(request)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# 任务分配API
@app.post("/api/food-safety/task/ai-advice", response_model=AIAdviceResponse)
async def get_ai_task_advice(request: AIAdviceRequest):
    """获取AI任务分配建议"""
    try:
        result = await task_service.get_ai_advice(request)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/food-safety/task/assign", response_model=TaskResponse)
async def assign_task(request: TaskAssignmentRequest):
    """分配任务"""
    try:
        result = await task_service.assign_task(request)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/food-safety/task/update-progress")
async def update_task_progress(request: TaskProgressRequest):
    """更新任务进度"""
    try:
        result = await task_service.update_progress(request)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# 台账管理API
@app.get("/api/food-safety/ledger/data")
async def get_ledger_data():
    """获取台账数据"""
    try:
        result = await ledger_service.get_all_records()
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/food-safety/ledger/ai-review", response_model=AIReviewResponse)
async def ai_review_ledger(data: List[dict]):
    """AI检查台账完整性"""
    try:
        result = await ledger_service.ai_review(data)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/food-safety/ledger/export")
async def export_ledger_data(request: LedgerExportRequest):
    """导出台账数据"""
    try:
        result = await ledger_service.export_data(request)
        
        # 创建Excel文件
        output = io.BytesIO()
        result.save(output)
        output.seek(0)
        
        return StreamingResponse(
            io.BytesIO(output.getvalue()),
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers={"Content-Disposition": "attachment; filename=食品安全台账.xlsx"}
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# 文件上传API
@app.post("/api/food-safety/upload")
async def upload_file(file: UploadFile = File(...)):
    """上传文件"""
    try:
        file_id = str(uuid.uuid4())
        file_info = {
            "id": file_id,
            "filename": file.filename,
            "content_type": file.content_type,
            "size": file.size
        }
        return {"success": True, "file": file_info}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# 健康检查
@app.get("/")
async def root():
    return {"message": "食品安全管理平台API服务运行正常"}

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)