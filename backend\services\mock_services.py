import json
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Any

class MockServices:
    """模拟服务类，用于前端开发测试"""
    
    @staticmethod
    def mock_ppt_blueprint():
        return {
            "structure": "食品安全培训PPT，包含背景介绍、法规要求、案例分析、总结建议四个部分",
            "slides": [
                {
                    "title": "食品安全培训",
                    "content": "面向餐饮企业员工的食品安全知识普及"
                },
                {
                    "title": "法规要求",
                    "content": "《食品安全法》相关条款解读"
                },
                {
                    "title": "案例分析",
                    "content": "近期食品安全事件案例分析"
                },
                {
                    "title": "总结建议",
                    "content": "企业食品安全管理建议"
                }
            ]
        }
    
    @staticmethod
    def mock_document():
        return {
            "title": "现场检查笔录",
            "content": """现场检查笔录

检查时间：2024年1月15日
检查地点：XX餐饮有限公司
检查人员：张三、李四
被检查单位：XX餐饮有限公司
法定代表人：王五

检查情况：
1. 厨房环境卫生状况良好
2. 食品储存符合规范要求
3. 从业人员健康证齐全有效
4. 未发现过期变质食品

处理意见：
本次检查未发现违法违规行为，予以通过。

检查人员签名：张三、李四
被检查单位签名：王五
日期：2024年1月15日"""
        }
    
    @staticmethod
    def mock_complaint_analysis():
        return {
            "summary": "消费者投诉某餐厅食品安全问题，涉及食品变质和服务态度",
            "suggestedTasks": [
                {
                    "description": "现场检查餐厅厨房卫生状况",
                    "priority": "high",
                    "deadline": (datetime.now() + timedelta(days=1)).strftime("%Y-%m-%d"),
                    "assignee": "张三"
                },
                {
                    "description": "调取餐厅进货台账和检验报告",
                    "priority": "medium",
                    "deadline": (datetime.now() + timedelta(days=2)).strftime("%Y-%m-%d"),
                    "assignee": "李四"
                },
                {
                    "description": "约谈餐厅负责人进行调查",
                    "priority": "high",
                    "deadline": (datetime.now() + timedelta(days=1)).strftime("%Y-%m-%d"),
                    "assignee": "王五"
                }
            ]
        }
    
    @staticmethod
    def mock_reply():
        return {
            "reply": """关于您反映的食品安全问题，我局已调查处理完毕，现将结果回复如下：

经查，您反映的XX餐厅确实存在部分食品安全管理不规范的问题。我局已责令该餐厅立即整改，具体措施包括：
1. 加强食品储存管理，确保食品新鲜
2. 规范食品加工操作流程
3. 加强从业人员培训

该餐厅已按要求完成整改，我局将继续加强监管。感谢您对食品安全工作的关注和支持。"""
        }
    
    @staticmethod
    def mock_ai_advice():
        return {
            "suggestions": [
                {"text": "建议分配给张三，他近期工作负载较轻"},
                {"text": "李四在食品安全检查方面有丰富经验"}
            ],
            "recommendedMembers": ["zhangsan", "lisi"]
        }
    
    @staticmethod
    def mock_task_response():
        return {
            "taskId": str(uuid.uuid4()),
            "success": True
        }
    
    @staticmethod
    def mock_ledger_data():
        return {
            "data": [
                {
                    "id": "REC001",
                    "type": "inspection",
                    "title": "XX餐厅日常检查",
                    "summary": "对XX餐厅进行日常食品安全检查",
                    "status": "completed",
                    "assignee": "张三",
                    "creator": "李四",
                    "createdAt": "2024-01-15",
                    "updatedAt": "2024-01-15",
                    "attachments": [
                        {"id": "ATT001", "name": "检查照片.jpg", "type": "jpg", "url": "/uploads/photo1.jpg"}
                    ],
                    "operationLogs": [
                        {"id": "LOG001", "time": "2024-01-15 09:00", "action": "创建记录", "operator": "李四"}
                    ]
                },
                {
                    "id": "REC002",
                    "type": "complaint",
                    "title": "消费者投诉处理",
                    "summary": "处理消费者关于食品安全的投诉",
                    "status": "in-progress",
                    "assignee": "王五",
                    "creator": "赵六",
                    "createdAt": "2024-01-14",
                    "updatedAt": "2024-01-15",
                    "attachments": [
                        {"id": "ATT002", "name": "投诉材料.pdf", "type": "pdf", "url": "/uploads/complaint.pdf"}
                    ],
                    "operationLogs": [
                        {"id": "LOG002", "time": "2024-01-14 10:00", "action": "创建记录", "operator": "赵六"},
                        {"id": "LOG003", "time": "2024-01-15 14:00", "action": "开始处理", "operator": "王五"}
                    ]
                }
            ]
        }
    
    @staticmethod
    def mock_ai_review():
        return {
            "reminders": [
                {
                    "id": "REM001",
                    "type": "缺失附件",
                    "message": "记录REC001缺少现场检查照片"
                },
                {
                    "id": "REM002",
                    "type": "过期提醒",
                    "message": "投诉记录REC002即将超过处理期限"
                }
            ]
        }