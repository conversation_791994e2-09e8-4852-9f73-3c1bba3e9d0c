<template>
  <div class="h-full">
    <VbenCard class="h-full" title="文书智能生成">
      <template #extra>
        <VbenButton type="primary" @click="handleGenerateDocument" :loading="generating">
          <template #icon>
            <lucide-icon name="file-text" />
          </template>
          生成文书
        </VbenButton>
      </template>

      <div class="grid h-full grid-cols-1 gap-4 lg:grid-cols-2">
        <!-- 左侧：输入区域 -->
        <div class="flex flex-col space-y-4">
          <VbenCard title="模板选择" class="flex-1">
            <VbenForm
              v-model:model="formData"
              :schema="templateSchema"
              :show-feedback="false"
              class="w-full"
            />
          </VbenCard>

          <VbenCard title="情况描述" class="flex-1">
            <VbenForm
              v-model:model="formData"
              :schema="descriptionSchema"
              :show-feedback="false"
              class="w-full"
            />
          </VbenCard>

          <VbenCard title="基本信息" class="flex-1">
            <VbenForm
              v-model:model="formData"
              :schema="basicInfoSchema"
              :show-feedback="false"
              class="w-full"
            />
          </VbenCard>
        </div>

        <!-- 右侧：预览区域 -->
        <div class="flex flex-col space-y-4">
          <VbenCard title="文书预览" class="flex-1">
            <div v-if="!documentContent" class="flex h-full min-h-[500px] items-center justify-center rounded border-2 border-dashed border-muted-foreground/25">
              <div class="text-center">
                <lucide-icon name="file-text" class="mx-auto h-12 w-12 text-muted-foreground" />
                <p class="mt-2 text-sm text-muted-foreground">
                  左侧选择模板并填写信息后生成文书
                </p>
              </div>
            </div>
            
            <div v-else class="h-full min-h-[500px] overflow-auto">
              <div class="rounded border bg-muted/50 p-4">
                <div class="flex items-center justify-between mb-4">
                  <h3 class="text-lg font-semibold">{{ documentTitle }}</h3>
                  <VbenTag :color="documentStatus === 'completed' ? 'success' : 'warning'">
                    {{ documentStatus === 'completed' ? '已生成' : '预览中' }}
                  </VbenTag>
                </div>
                
                <div class="prose prose-sm max-w-none">
                  <div v-html="formattedDocument" class="whitespace-pre-wrap rounded bg-white p-4 text-sm"></div>
                </div>
              </div>
            </div>
          </VbenCard>

          <div class="flex space-x-2" v-if="documentContent">
            <VbenButton type="primary" @click="handleDownloadDocument">
              <template #icon>
                <lucide-icon name="download" />
              </template>
              下载文书
            </VbenButton>
            
            <VbenButton @click="handleEditDocument">
              <template #icon>
                <lucide-icon name="edit" />
              </template>
              编辑内容
            </VbenButton>
            
            <VbenButton @click="handleRegenerateDocument">
              <template #icon>
                <lucide-icon name="refresh" />
              </template>
              重新生成
            </VbenButton>
          </div>
        </div>
      </div>

      <!-- 编辑对话框 -->
      <VbenModal
        v-model:show="showEditModal"
        title="编辑文书内容"
        :width="800"
      >
        <div class="space-y-4">
          <VbenTextarea
            v-model="editContent"
            rows="20"
            class="w-full"
            placeholder="编辑文书内容..."
          />
          
          <div class="flex justify-end space-x-2">
            <VbenButton @click="showEditModal = false">
              取消
            </VbenButton>
            <VbenButton type="primary" @click="handleSaveEdit">
              保存修改
            </VbenButton>
          </div>
        </div>
      </VbenModal>
    </VbenCard>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useMessage } from '@vben/hooks';
import { generateDocument } from '#/api/food-safety';

const { createMessage } = useMessage();

const formData = ref({
  templateType: 'inspection-record',
  caseType: 'routine',
  companyName: '',
  companyAddress: '',
  legalPerson: '',
  inspectionDate: '',
  inspectors: '',
  violationType: '',
  violationDescription: '',
  penaltyMeasures: '',
  deadline: '',
  additionalInfo: '',
});

const generating = ref(false);
const documentContent = ref('');
const documentTitle = ref('');
const documentStatus = ref('preview');
const showEditModal = ref(false);
const editContent = ref('');

const templateSchema = [
  {
    component: 'VbenSelect',
    componentProps: {
      options: [
        { label: '现场检查笔录', value: 'inspection-record' },
        { label: '责令改正通知书', value: 'rectification-notice' },
        { label: '行政处罚决定书', value: 'penalty-decision' },
        { label: '询问笔录', value: 'interrogation-record' },
        { label: '抽样检验告知书', value: 'sampling-notice' },
        { label: '投诉处理回复', value: 'complaint-reply' },
      ],
    },
    fieldName: 'templateType',
    label: '文书类型',
  },
  {
    component: 'VbenSelect',
    componentProps: {
      options: [
        { label: '日常检查', value: 'routine' },
        { label: '专项检查', value: 'special' },
        { label: '投诉举报', value: 'complaint' },
        { label: '抽检不合格', value: 'sampling-fail' },
        { label: '重大活动保障', value: 'event-security' },
      ],
    },
    fieldName: 'caseType',
    label: '案件类型',
  },
];

const basicInfoSchema = [
  {
    component: 'VbenInput',
    componentProps: {
      placeholder: '请输入企业名称',
    },
    fieldName: 'companyName',
    label: '企业名称',
  },
  {
    component: 'VbenInput',
    componentProps: {
      placeholder: '请输入企业地址',
    },
    fieldName: 'companyAddress',
    label: '企业地址',
  },
  {
    component: 'VbenInput',
    componentProps: {
      placeholder: '请输入法人代表',
    },
    fieldName: 'legalPerson',
    label: '法人代表',
  },
  {
    component: 'VbenDatePicker',
    componentProps: {
      placeholder: '选择检查日期',
    },
    fieldName: 'inspectionDate',
    label: '检查日期',
  },
  {
    component: 'VbenInput',
    componentProps: {
      placeholder: '请输入检查人员',
    },
    fieldName: 'inspectors',
    label: '检查人员',
  },
];

const descriptionSchema = [
  {
    component: 'VbenSelect',
    componentProps: {
      options: [
        { label: '无违规行为', value: 'none' },
        { label: '环境卫生问题', value: 'environment' },
        { label: '食品质量问题', value: 'quality' },
        { label: '标签标识问题', value: 'labeling' },
        { label: '进货查验问题', value: 'procurement' },
        { label: '人员健康问题', value: 'health' },
        { label: '其他', value: 'other' },
      ],
    },
    fieldName: 'violationType',
    label: '违规类型',
  },
  {
    component: 'VbenTextarea',
    componentProps: {
      rows: 4,
      placeholder: '详细描述违规情况...',
    },
    fieldName: 'violationDescription',
    label: '违规描述',
  },
  {
    component: 'VbenTextarea',
    componentProps: {
      rows: 3,
      placeholder: '填写处罚措施...',
    },
    fieldName: 'penaltyMeasures',
    label: '处罚措施',
  },
  {
    component: 'VbenDatePicker',
    componentProps: {
      placeholder: '选择整改期限',
    },
    fieldName: 'deadline',
    label: '整改期限',
  },
  {
    component: 'VbenTextarea',
    componentProps: {
      rows: 3,
      placeholder: '其他补充信息...',
    },
    fieldName: 'additionalInfo',
    label: '补充信息',
  },
];

const formattedDocument = computed(() => {
  if (!documentContent.value) return '';
  return documentContent.value
    .replace(/\n/g, '<br/>')
    .replace(/\t/g, '&nbsp;&nbsp;&nbsp;&nbsp;');
});

const handleGenerateDocument = async () => {
  const requiredFields = ['templateType', 'companyName', 'inspectionDate'];
  const missingFields = requiredFields.filter(
    (field) => !formData.value[field as keyof typeof formData.value],
  );

  if (missingFields.length > 0) {
    createMessage.error('请填写必要信息：企业名称、检查日期等');
    return;
  }

  generating.value = true;
  try {
    const result = await generateDocument(formData.value);
    documentContent.value = result.content;
    documentTitle.value = result.title;
    documentStatus.value = 'completed';
    createMessage.success('文书生成成功');
  } catch (error) {
    createMessage.error('生成失败，请重试');
  } finally {
    generating.value = false;
  }
};

const handleDownloadDocument = () => {
  if (!documentContent.value) return;

  const blob = new Blob([documentContent.value], {
    type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `${documentTitle.value}.docx`;
  a.click();
  URL.revokeObjectURL(url);
  createMessage.success('下载成功');
};

const handleEditDocument = () => {
  editContent.value = documentContent.value;
  showEditModal.value = true;
};

const handleSaveEdit = () => {
  documentContent.value = editContent.value;
  showEditModal.value = false;
  createMessage.success('修改已保存');
};

const handleRegenerateDocument = () => {
  documentContent.value = '';
  documentTitle.value = '';
  documentStatus.value = 'preview';
};
</script>