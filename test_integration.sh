#!/bin/bash

# 食品安全管理平台集成测试脚本
echo "=== 食品安全管理平台集成测试 ==="

# 检查前端项目结构
echo "1. 检查前端项目结构..."
ls -la playground/src/views/food-safety/
echo ""

# 检查路由配置
echo "2. 检查路由配置..."
cat playground/src/router/routes/modules/food-safety.ts
echo ""

# 检查API定义
echo "3. 检查API定义..."
cat playground/src/api/food-safety/index.ts
echo ""

# 检查后端结构
echo "4. 检查后端结构..."
ls -la backend/
echo ""

# 显示设置指南
echo "5. 设置指南..."
cat setup.md
echo ""

echo "=== 集成测试完成 ==="
echo ""
echo "下一步操作："
echo "1. 安装Python依赖: pip install -r backend/requirements.txt"
echo "2. 启动后端服务: python3 backend/mock_server.py"
echo "3. 启动前端服务: pnpm dev:play"
echo "4. 访问 http://localhost:5173 测试系统"