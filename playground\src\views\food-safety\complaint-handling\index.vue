<template>
  <div class="h-full">
    <VbenCard class="h-full" title="投诉举报处理">
      <template #extra>
        <VbenButton type="primary" @click="handleCreateComplaint">
          <template #icon>
            <lucide-icon name="plus" />
          </template>
          新建投诉
        </VbenButton>
      </template>

      <!-- 步骤条 -->
      <div class="mb-6">
        <VbenSteps :current="currentStep" :items="steps" />
      </div>

      <!-- 第1步：上传材料 -->
      <div v-if="currentStep === 0" class="space-y-4">
        <VbenCard title="投诉信息录入">
          <VbenForm
            v-model:model="complaintForm"
            :schema="complaintSchema"
            :show-feedback="false"
            class="w-full"
          />
        </VbenCard>

        <VbenCard title="上传材料">
          <div class="space-y-4">
            <VbenUpload
              v-model:file-list="evidenceFiles"
              accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
              :multiple="true"
              :max-count="10"
              class="w-full"
            >
              <VbenButton type="default">
                <template #icon>
                  <lucide-icon name="upload" />
                </template>
                选择文件
              </VbenButton>
            </VbenUpload>

            <div class="text-sm text-muted-foreground">
              支持格式：PDF、图片、Word文档，最多10个文件
            </div>
          </div>
        </VbenCard>

        <div class="flex justify-end">
          <VbenButton type="primary" @click="handleUploadComplete" :loading="analyzing">
            分析材料
          </VbenButton>
        </div>
      </div>

      <!-- 第2步：AI分析生成任务 -->
      <div v-if="currentStep === 1" class="space-y-4">
        <VbenCard title="AI分析结果">
          <div v-if="analysisResult" class="space-y-4">
            <div class="rounded bg-muted p-4">
              <h4 class="mb-2 font-semibold">投诉要点总结：</h4>
              <p class="text-sm text-muted-foreground">{{ analysisResult.summary }}</p>
            </div>

            <div>
              <h4 class="mb-2 font-semibold">建议调查任务：</h4>
              <div class="space-y-2">
                <div
                  v-for="(task, index) in suggestedTasks"
                  :key="index"
                  class="flex items-center justify-between rounded border p-3"
                >
                  <div class="flex items-center space-x-2">
                    <VbenCheckbox
                      v-model:checked="task.selected"
                      :id="`task-${index}`"
                    />
                    <label :for="`task-${index}`" class="text-sm">
                      {{ task.description }}
                    </label>
                  </div>
                  <VbenTag :color="task.priority === 'high' ? 'error' : 'warning'">
                    {{ task.priority === 'high' ? '高优先级' : '中优先级' }}
                  </VbenTag>
                </div>
              </div>
            </div>
          </div>
        </VbenCard>

        <div class="flex justify-between">
          <VbenButton @click="currentStep = 0">上一步</VbenButton>
          <VbenButton type="primary" @click="handleCreateTasks">创建任务</VbenButton>
        </div>
      </div>

      <!-- 第3步：任务执行 -->
      <div v-if="currentStep === 2" class="space-y-4">
        <VbenCard title="任务执行进度">
          <VbenTable
            :columns="taskColumns"
            :data="tasks"
            :pagination="false"
            class="w-full"
          >
            <template #action="{ row }">
              <VbenButton
                size="small"
                type="primary"
                @click="handleCompleteTask(row)"
                :disabled="row.status === 'completed'"
              >
                完成
              </VbenButton>
            </template>
          </VbenTable>
        </VbenCard>

        <div class="flex justify-between">
          <VbenButton @click="currentStep = 1">上一步</VbenButton>
          <VbenButton type="primary" @click="handleAllTasksComplete" :disabled="!allTasksCompleted">
            所有任务完成
          </VbenButton>
        </div>
      </div>

      <!-- 第4步：生成回复 -->
      <div v-if="currentStep === 3" class="space-y-4">
        <VbenCard title="生成投诉回复">
          <div class="space-y-4">
            <div>
              <h4 class="mb-2 font-semibold">调查结果总结：</h4>
              <VbenTextarea
                v-model="investigationResult"
                rows="4"
                placeholder="请填写调查结果总结..."
              />
            </div>

            <div>
              <h4 class="mb-2 font-semibold">AI生成回复草稿：</h4>
              <div v-if="replyDraft" class="rounded border bg-muted/50 p-4">
                <div class="prose prose-sm max-w-none">
                  <div v-html="formattedReply" class="whitespace-pre-wrap text-sm"></div>
                </div>
              </div>
              <div v-else class="flex h-32 items-center justify-center rounded border">
                <p class="text-sm text-muted-foreground">点击生成回复草稿</p>
              </div>
            </div>

            <div class="flex space-x-2">
              <VbenButton type="primary" @click="handleGenerateReply" :loading="generatingReply">
                生成回复
              </VbenButton>
              <VbenButton @click="handleEditReply" v-if="replyDraft">
                编辑回复
              </VbenButton>
            </div>
          </div>
        </VbenCard>

        <div class="flex justify-between">
          <VbenButton @click="currentStep = 2">上一步</VbenButton>
          <VbenButton type="primary" @click="handleSendReply" :disabled="!replyDraft">
            发送回复
          </VbenButton>
        </div>
      </div>

      <!-- 编辑回复对话框 -->
      <VbenModal
        v-model:show="showReplyModal"
        title="编辑回复内容"
        :width="600"
      >
        <div class="space-y-4">
          <VbenTextarea
            v-model="editedReply"
            rows="10"
            class="w-full"
            placeholder="编辑回复内容..."
          />
          
          <div class="flex justify-end space-x-2">
            <VbenButton @click="showReplyModal = false">取消</VbenButton>
            <VbenButton type="primary" @click="handleSaveReply">保存</VbenButton>
          </div>
        </div>
      </VbenModal>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useMessage } from '@vben/hooks';
import { analyzeComplaint, generateReply } from '#/api/food-safety';

interface Task {
  id: string;
  description: string;
  priority: 'high' | 'medium' | 'low';
  status: 'pending' | 'in-progress' | 'completed';
  deadline: string;
  assignee: string;
}

const { createMessage } = useMessage();

const currentStep = ref(0);
const steps = [
  { title: '上传材料', description: '上传投诉相关材料' },
  { title: 'AI分析', description: 'AI分析并生成任务' },
  { title: '任务执行', description: '执行调查任务' },
  { title: '生成回复', description: '生成投诉回复' },
];

const complaintForm = ref({
  complainantName: '',
  complainantPhone: '',
  complainantAddress: '',
  respondentName: '',
  respondentAddress: '',
  complaintType: 'food-safety',
  complaintDescription: '',
  complaintDate: '',
  evidenceDescription: '',
});

const evidenceFiles = ref([]);
const analyzing = ref(false);
const analysisResult = ref();
const suggestedTasks = ref<Array<Task & { selected: boolean }>>([]);
const tasks = ref<Task[]>([]);
const investigationResult = ref('');
const replyDraft = ref('');
const generatingReply = ref(false);
const showReplyModal = ref(false);
const editedReply = ref('');

const complaintSchema = [
  {
    component: 'VbenInput',
    componentProps: {
      placeholder: '请输入投诉人姓名',
    },
    fieldName: 'complainantName',
    label: '投诉人姓名',
    rules: 'required',
  },
  {
    component: 'VbenInput',
    componentProps: {
      placeholder: '请输入联系电话',
    },
    fieldName: 'complainantPhone',
    label: '联系电话',
    rules: 'required|phone',
  },
  {
    component: 'VbenInput',
    componentProps: {
      placeholder: '请输入联系地址',
    },
    fieldName: 'complainantAddress',
    label: '联系地址',
  },
  {
    component: 'VbenInput',
    componentProps: {
      placeholder: '请输入被投诉单位名称',
    },
    fieldName: 'respondentName',
    label: '被投诉单位',
    rules: 'required',
  },
  {
    component: 'VbenInput',
    componentProps: {
      placeholder: '请输入被投诉单位地址',
    },
    fieldName: 'respondentAddress',
    label: '单位地址',
  },
  {
    component: 'VbenSelect',
    componentProps: {
      options: [
        { label: '食品安全问题', value: 'food-safety' },
        { label: '价格欺诈', value: 'price-fraud' },
        { label: '虚假宣传', value: 'false-advertising' },
        { label: '服务质量', value: 'service-quality' },
        { label: '其他', value: 'other' },
      ],
    },
    fieldName: 'complaintType',
    label: '投诉类型',
  },
  {
    component: 'VbenDatePicker',
    componentProps: {
      placeholder: '选择投诉日期',
    },
    fieldName: 'complaintDate',
    label: '投诉日期',
  },
  {
    component: 'VbenTextarea',
    componentProps: {
      rows: 4,
      placeholder: '详细描述投诉内容...',
    },
    fieldName: 'complaintDescription',
    label: '投诉描述',
    rules: 'required',
  },
];

const taskColumns = [
  {
    title: '任务描述',
    key: 'description',
  },
  {
    title: '优先级',
    key: 'priority',
    render: ({ row }) => (
      <VbenTag color={row.priority === 'high' ? 'error' : 'warning'}>
        {row.priority === 'high' ? '高' : '中'}
      </VbenTag>
    ),
  },
  {
    title: '状态',
    key: 'status',
    render: ({ row }) => (
      <VbenTag color={row.status === 'completed' ? 'success' : 'processing'}>
        {row.status === 'completed' ? '已完成' : '待处理'}
      </VbenTag>
    ),
  },
  {
    title: '负责人',
    key: 'assignee',
  },
  {
    title: '截止日期',
    key: 'deadline',
  },
  {
    title: '操作',
    key: 'action',
    width: 100,
  },
];

const allTasksCompleted = computed(() =>
  tasks.value.every(task => task.status === 'completed')
);

const formattedReply = computed(() =>
  replyDraft.value.replace(/\n/g, '<br/>')
);

const handleCreateComplaint = () => {
  currentStep.value = 0;
  complaintForm.value = {
    complainantName: '',
    complainantPhone: '',
    complainantAddress: '',
    respondentName: '',
    respondentAddress: '',
    complaintType: 'food-safety',
    complaintDescription: '',
    complaintDate: '',
    evidenceDescription: '',
  };
  evidenceFiles.value = [];
  analysisResult.value = null;
  suggestedTasks.value = [];
  tasks.value = [];
  investigationResult.value = '';
  replyDraft.value = '';
};

const handleUploadComplete = async () => {
  if (!complaintForm.value.complaintDescription.trim()) {
    createMessage.error('请填写投诉描述');
    return;
  }

  analyzing.value = true;
  try {
    const result = await analyzeComplaint({
      complaint: complaintForm.value,
      evidenceFiles: evidenceFiles.value,
    });

    analysisResult.value = result;
    suggestedTasks.value = result.suggestedTasks.map(task => ({
      ...task,
      selected: true,
    }));
    currentStep.value = 1;
  } catch (error) {
    createMessage.error('分析失败，请重试');
  } finally {
    analyzing.value = false;
  }
};

const handleCreateTasks = () => {
  tasks.value = suggestedTasks.value
    .filter(task => task.selected)
    .map(task => ({
      ...task,
      id: Date.now().toString() + Math.random(),
      status: 'pending',
    }));
  currentStep.value = 2;
};

const handleCompleteTask = (task: Task) => {
  task.status = 'completed';
  createMessage.success('任务已完成');
};

const handleAllTasksComplete = () => {
  currentStep.value = 3;
};

const handleGenerateReply = async () => {
  if (!investigationResult.value.trim()) {
    createMessage.error('请填写调查结果总结');
    return;
  }

  generatingReply.value = true;
  try {
    const result = await generateReply({
      complaint: complaintForm.value,
      investigationResult: investigationResult.value,
      completedTasks: tasks.value.filter(task => task.status === 'completed'),
    });

    replyDraft.value = result.reply;
    createMessage.success('回复草稿已生成');
  } catch (error) {
    createMessage.error('生成失败，请重试');
  } finally {
    generatingReply.value = false;
  }
};

const handleEditReply = () => {
  editedReply.value = replyDraft.value;
  showReplyModal.value = true;
};

const handleSaveReply = () => {
  replyDraft.value = editedReply.value;
  showReplyModal.value = false;
  createMessage.success('修改已保存');
};

const handleSendReply = async () => {
  try {
    // 发送回复的逻辑
    createMessage.success('投诉回复已发送');
    // 重置状态
    handleCreateComplaint();
  } catch (error) {
    createMessage.error('发送失败，请重试');
  }
};
</script>