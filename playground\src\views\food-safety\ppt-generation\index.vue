<template>
  <div class="h-full">
    <VbenCard class="h-full" title="PPT智能生成">
      <template #extra>
        <VbenButton type="primary" @click="handleGeneratePPT" :loading="generating">
          <template #icon>
            <lucide-icon name="sparkles" />
          </template>
          生成PPT
        </VbenButton>
      </template>

      <div class="grid h-full grid-cols-1 gap-4 lg:grid-cols-2">
        <!-- 左侧：输入区域 -->
        <div class="flex flex-col space-y-4">
          <VbenCard title="需求输入" class="flex-1">
            <VbenForm
              v-model:model="formData"
              :schema="inputSchema"
              :show-feedback="false"
              class="w-full"
            />
          </VbenCard>

          <VbenCard title="上传附件" class="flex-1">
            <div class="flex flex-col space-y-4">
              <VbenUpload
                v-model:file-list="fileList"
                accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png"
                :multiple="true"
                :max-count="5"
                class="w-full"
              >
                <VbenButton type="default">
                  <template #icon>
                    <lucide-icon name="upload" />
                  </template>
                  选择文件
                </VbenButton>
              </VbenUpload>
              
              <div v-if="attachments.length > 0" class="space-y-2">
                <div class="text-sm font-medium">已上传文件：</div>
                <div class="space-y-1">
                  <div
                    v-for="file in attachments"
                    :key="file.uid"
                    class="flex items-center justify-between rounded border p-2"
                  >
                    <div class="flex items-center space-x-2">
                      <lucide-icon name="file-text" class="h-4 w-4" />
                      <span class="text-sm">{{ file.name }}</span>
                    </div>
                    <VbenButton
                      type="text"
                      size="small"
                      @click="handleRemoveFile(file.uid)"
                    >
                      <lucide-icon name="x" class="h-4 w-4" />
                    </VbenButton>
                  </div>
                </div>
              </div>
            </div>
          </VbenCard>

          <VbenCard title="AI生成蓝图" v-if="blueprint">
            <div class="space-y-4">
              <div class="rounded bg-muted p-4">
                <div class="text-sm font-medium mb-2">PPT结构：</div>
                <div class="text-sm text-muted-foreground">{{ blueprint.structure }}</div>
              </div>
              
              <div class="space-y-2">
                <div class="text-sm font-medium">幻灯片预览：</div>
                <div class="space-y-2">
                  <div
                    v-for="(slide, index) in blueprint.slides"
                    :key="index"
                    class="rounded border p-3 text-sm"
                  >
                    <div class="font-medium">{{ slide.title }}</div>
                    <div class="text-xs text-muted-foreground">{{ slide.content }}</div>
                  </div>
                </div>
              </div>

              <div class="flex space-x-2">
                <VbenButton
                  size="small"
                  @click="handleModifyBlueprint"
                >
                  修改蓝图
                </VbenButton>
                <VbenButton
                  size="small"
                  type="primary"
                  @click="handleConfirmBlueprint"
                >
                  确认制作
                </VbenButton>
              </div>
            </div>
          </VbenCard>
        </div>

        <!-- 右侧：预览区域 -->
        <div class="flex flex-col space-y-4">
          <VbenCard title="PPT预览" class="flex-1">
            <div v-if="!pptUrl" class="flex h-full min-h-[400px] items-center justify-center rounded border-2 border-dashed border-muted-foreground/25">
              <div class="text-center">
                <lucide-icon name="presentation" class="mx-auto h-12 w-12 text-muted-foreground" />
                <p class="mt-2 text-sm text-muted-foreground">
                  左侧输入需求并生成PPT后将在此处显示预览
                </p>
              </div>
            </div>
            
            <div v-else class="h-full min-h-[400px]">
              <iframe
                :src="pptUrl"
                class="h-full w-full rounded border"
                frameborder="0"
              />
            </div>
          </VbenCard>

          <VbenCard title="对话修改" v-if="showChat">
            <div class="space-y-4">
              <div class="h-32 overflow-y-auto rounded border p-3">
                <div
                  v-for="(message, index) in chatMessages"
                  :key="index"
                  :class="[
                    'mb-2 text-sm',
                    message.role === 'user' ? 'text-right' : 'text-left',
                  ]"
                >
                  <span
                    :class="[
                      'inline-block rounded px-2 py-1',
                      message.role === 'user'
                        ? 'bg-primary text-primary-foreground'
                        : 'bg-muted',
                    ]"
                  >
                    {{ message.content }}
                  </span>
                </div>
              </div>
              
              <div class="flex space-x-2">
                <VbenInput
                  v-model="chatInput"
                  placeholder="输入修改需求..."
                  class="flex-1"
                  @keyup.enter="handleSendMessage"
                />
                <VbenButton @click="handleSendMessage" :loading="chatLoading">
                  发送
                </VbenButton>
              </div>
            </div>
          </VbenCard>

          <div class="flex space-x-2" v-if="pptGenerated">
            <VbenButton type="primary" @click="handleDownloadPPT">
              <template #icon>
                <lucide-icon name="download" />
              </template>
              下载PPT
            </VbenButton>
            <VbenButton @click="handleRegeneratePPT">
              <template #icon>
                <lucide-icon name="refresh" />
              </template>
              重新生成
            </VbenButton>
          </div>
        </div>
      </div>
    </VbenCard>

    <!-- 修改蓝图对话框 -->
    <VbenModal
      v-model:show="showBlueprintModal"
      title="修改PPT蓝图"
      :width="600"
    >
      <div class="space-y-4">
        <VbenForm
          v-model:model="blueprintForm"
          :schema="blueprintSchema"
          :show-feedback="false"
        />
        
        <div class="flex justify-end space-x-2">
          <VbenButton @click="showBlueprintModal = false">
            取消
          </VbenButton>
          <VbenButton type="primary" @click="handleSaveBlueprint">
            保存修改
          </VbenButton>
        </div>
      </div>
    </VbenModal>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useMessage } from '@vben/hooks';
import { generatePPT } from '#/api/food-safety';

interface Blueprint {
  structure: string;
  slides: Array<{ title: string; content: string }>;
}

interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
}

const { createMessage } = useMessage();

const formData = ref({
  requirements: '',
  templateStyle: 'formal',
  theme: 'food-safety',
});

const fileList = ref([]);
const attachments = ref<Array<{ uid: string; name: string }>>([]);
const generating = ref(false);
const blueprint = ref<Blueprint | null>(null);
const pptUrl = ref('');
const pptGenerated = ref(false);
const showChat = ref(false);
const showBlueprintModal = ref(false);
const chatMessages = ref<ChatMessage[]>([]);
const chatInput = ref('');
const chatLoading = ref(false);
const blueprintForm = ref({
  structure: '',
  slides: [] as Array<{ title: string; content: string }>,
});

const inputSchema = [
  {
    component: 'VbenTextarea',
    componentProps: {
      rows: 4,
      placeholder: '请描述您的PPT需求，例如：\n- 食品安全培训材料\n- 执法案例总结\n- 政策宣贯内容\n支持自然语言描述...',
    },
    fieldName: 'requirements',
    label: '需求描述',
    rules: 'required',
  },
  {
    component: 'VbenSelect',
    componentProps: {
      options: [
        { label: '正式', value: 'formal' },
        { label: '简洁', value: 'simple' },
        { label: '生动', value: 'vivid' },
      ],
    },
    fieldName: 'templateStyle',
    label: '模板风格',
  },
  {
    component: 'VbenSelect',
    componentProps: {
      options: [
        { label: '食品安全', value: 'food-safety' },
        { label: '执法案例', value: 'enforcement' },
        { label: '政策宣贯', value: 'policy' },
      ],
    },
    fieldName: 'theme',
    label: '主题类型',
  },
];

const blueprintSchema = [
  {
    component: 'VbenTextarea',
    componentProps: {
      rows: 3,
      placeholder: '修改PPT整体结构描述...',
    },
    fieldName: 'structure',
    label: '结构描述',
  },
  {
    component: 'VbenDynamicInput',
    componentProps: {
      schema: [
        {
          component: 'VbenInput',
          componentProps: {
            placeholder: '幻灯片标题',
          },
          fieldName: 'title',
          label: '标题',
        },
        {
          component: 'VbenTextarea',
          componentProps: {
            rows: 2,
            placeholder: '幻灯片内容',
          },
          fieldName: 'content',
          label: '内容',
        },
      ],
    },
    fieldName: 'slides',
    label: '幻灯片列表',
  },
];

const handleGeneratePPT = async () => {
  if (!formData.value.requirements.trim()) {
    createMessage.error('请输入需求描述');
    return;
  }

  generating.value = true;
  try {
    const result = await generatePPT({
      requirements: formData.value.requirements,
      templateStyle: formData.value.templateStyle,
      theme: formData.value.theme,
      attachments: attachments.value,
    });

    blueprint.value = result.blueprint;
    createMessage.success('PPT蓝图生成成功，请确认后制作');
  } catch (error) {
    createMessage.error('生成失败，请重试');
  } finally {
    generating.value = false;
  }
};

const handleRemoveFile = (uid: string) => {
  attachments.value = attachments.value.filter((file) => file.uid !== uid);
};

const handleConfirmBlueprint = async () => {
  generating.value = true;
  try {
    const result = await generatePPT({
      requirements: formData.value.requirements,
      blueprint: blueprint.value,
      confirm: true,
    });

    pptUrl.value = result.url;
    pptGenerated.value = true;
    showChat.value = true;
    createMessage.success('PPT生成成功');
  } catch (error) {
    createMessage.error('生成失败，请重试');
  } finally {
    generating.value = false;
  }
};

const handleModifyBlueprint = () => {
  if (blueprint.value) {
    blueprintForm.value.structure = blueprint.value.structure;
    blueprintForm.value.slides = [...blueprint.value.slides];
    showBlueprintModal.value = true;
  }
};

const handleSaveBlueprint = () => {
  if (blueprint.value) {
    blueprint.value.structure = blueprintForm.value.structure;
    blueprint.value.slides = [...blueprintForm.value.slides];
    showBlueprintModal.value = false;
    createMessage.success('蓝图已更新');
  }
};

const handleSendMessage = async () => {
  if (!chatInput.value.trim()) return;

  chatMessages.value.push({ role: 'user', content: chatInput.value });
  const message = chatInput.value;
  chatInput.value = '';
  chatLoading.value = true;

  try {
    // 调用AI对话接口
    const response = await generatePPT({
      chat: message,
      context: chatMessages.value,
    });

    chatMessages.value.push({ role: 'assistant', content: response.reply });
    
    if (response.updatedPptUrl) {
      pptUrl.value = response.updatedPptUrl;
    }
  } catch (error) {
    createMessage.error('对话失败，请重试');
  } finally {
    chatLoading.value = false;
  }
};

const handleDownloadPPT = () => {
  if (pptUrl.value) {
    window.open(pptUrl.value, '_blank');
  }
};

const handleRegeneratePPT = () => {
  blueprint.value = null;
  pptUrl.value = '';
  pptGenerated.value = false;
  showChat.value = false;
  chatMessages.value = [];
  createMessage.info('请重新输入需求');
};
</script>