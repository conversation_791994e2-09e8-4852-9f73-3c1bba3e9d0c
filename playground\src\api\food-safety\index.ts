import { requestClient } from '#/api/request';

const prefix = '/api/food-safety';

// PPT生成相关API
export const generatePPT = (data: any) => {
  return requestClient.post(`${prefix}/ppt/generate`, data);
};

// 文书生成相关API
export const generateDocument = (data: any) => {
  return requestClient.post(`${prefix}/document/generate`, data);
};

// 投诉处理相关API
export const analyzeComplaint = (data: any) => {
  return requestClient.post(`${prefix}/complaint/analyze`, data);
};

export const generateReply = (data: any) => {
  return requestClient.post(`${prefix}/complaint/generate-reply`, data);
};

// 任务分配相关API
export const getAIAdvice = (data: any) => {
  return requestClient.post(`${prefix}/task/ai-advice`, data);
};

export const assignTask = (data: any) => {
  return requestClient.post(`${prefix}/task/assign`, data);
};

export const updateTaskProgress = (data: any) => {
  return requestClient.post(`${prefix}/task/update-progress`, data);
};

// 台账管理相关API
export const getLedgerData = () => {
  return requestClient.get(`${prefix}/ledger/data`);
};

export const getAIReview = (data: any) => {
  return requestClient.post(`${prefix}/ledger/ai-review`, data);
};

export const exportLedgerData = (data: any) => {
  return requestClient.post(`${prefix}/ledger/export`, data, {
    responseType: 'blob',
  });
};

// 通用API
export const uploadFile = (file: File) => {
  const formData = new FormData();
  formData.append('file', file);
  return requestClient.post(`${prefix}/upload`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};